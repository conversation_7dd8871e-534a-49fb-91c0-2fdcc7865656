import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { User, Crown, Eye, Camera } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useHubParticipants } from '@/hooks/collaboration-hubs'
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { HubParticipantResponse } from '@/components/collaboration-hub/types'

interface MentionInputProps {
  hubId: number
  value: string // This is now the email-based raw value (source of truth)
  onChange: (value: string) => void // Always receives email-based raw value
  onKeyDown?: (e: React.KeyboardEvent) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  children: (props: {
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>
    value: string // Email-based raw value (source of truth)
    displayValue: string // Name-based display value for UI
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
    onKeyDown: (e: React.KeyboardEvent) => void
    onSelect: (e: React.SyntheticEvent) => void
    placeholder?: string
    disabled?: boolean
    className?: string
  }) => React.ReactNode
}

interface MentionMatch {
  start: number
  end: number
  query: string
}

// Stores mention data for dual state management
interface MentionData {
  participant: HubParticipantResponse
  start: number
  end: number
  displayText: string // @John Doe
  rawText: string // @<EMAIL>
}

export function MentionInput({
                               hubId,
                               value,
                               onChange,
                               onKeyDown,
                               placeholder,
                               disabled,
                               className,
                               children
                             }: MentionInputProps) {
  const { t, keys } = useTranslations()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [mentionMatch, setMentionMatch] = useState<MentionMatch | null>(null)
  const [mentions, setMentions] = useState<MentionData[]>([])
  const [rawValue, setRawValue] = useState<string>(value) // Email-based value (source of truth)
  const [displayValue, setDisplayValue] = useState<string>('') // Name-based value (derived)
  const [isInsertingMention, setIsInsertingMention] = useState(false)
  const [completedMentions, setCompletedMentions] = useState<Set<number>>(new Set())
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)

  const { data: participantsData, isLoading } = useHubParticipants(hubId, {
    enabled: !!hubId && !disabled,
    size: 100
  })

  const filteredParticipants = useMemo(() => {
    if (!mentionMatch) return []

    const participants = participantsData?.content || []
    return participants.filter(p => {
      const query = mentionMatch.query.toLowerCase()
      const emailPrefix = p.email?.split('@')[0]?.toLowerCase() || ''
      const name = p.name?.toLowerCase() || ''

      // Prioritize exact email prefix matches
      return emailPrefix.includes(query) || name.includes(query)
    }).sort((a, b) => {
      const query = mentionMatch.query.toLowerCase()
      const aEmailPrefix = a.email?.split('@')[0]?.toLowerCase() || ''
      const bEmailPrefix = b.email?.split('@')[0]?.toLowerCase() || ''

      // Prioritize exact email prefix matches first
      const aEmailMatch = aEmailPrefix.startsWith(query)
      const bEmailMatch = bEmailPrefix.startsWith(query)

      if (aEmailMatch && !bEmailMatch) return -1
      if (!aEmailMatch && bEmailMatch) return 1

      // Then prioritize email prefix contains over name matches
      const aEmailContains = aEmailPrefix.includes(query)
      const bEmailContains = bEmailPrefix.includes(query)

      if (aEmailContains && !bEmailContains) return -1
      if (!aEmailContains && bEmailContains) return 1

      return 0
    })
  }, [mentionMatch, participantsData?.content])

  // Handle click outside to close popup
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen &&
          popoverRef.current &&
          !popoverRef.current.contains(event.target as Node) &&
          inputRef.current &&
          !inputRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-3 w-3 text-yellow-600" />
      case 'reviewer':
        return <Eye className="h-3 w-3 text-blue-600" />
      case 'reviewer_creator':
        return <Camera className="h-3 w-3 text-purple-600" />
      case 'content_creator':
        return <User className="h-3 w-3 text-green-600" />
      default:
        return <User className="h-3 w-3 text-gray-600" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return t(keys.collaborationHubs.roles.admin)
      case 'reviewer':
        return t(keys.collaborationHubs.roles.reviewer)
      case 'reviewer_creator':
        return t(keys.collaborationHubs.roles.reviewer_creator)
      case 'content_creator':
        return t(keys.collaborationHubs.roles.content_creator)
      default:
        return role
    }
  }

  const findMentionMatch = useCallback((text: string, cursor: number): MentionMatch | null => {
    const before = text.slice(0, cursor)

    // Find the last @ symbol before cursor
    const lastAtIndex = before.lastIndexOf('@')
    if (lastAtIndex === -1) return null

    // Get text after the @ symbol
    const afterAt = before.slice(lastAtIndex + 1)

    // Check if this position is in our completed mentions set
    if (completedMentions.has(lastAtIndex)) {
      return null
    }

    // Don't match if there's a space at the end (completed mention)
    if (afterAt.endsWith(' ')) {
      return null
    }

    // Match valid mention characters (letters, numbers, dots, underscores, spaces for multi-word names)
    // This works on display text but we'll convert to email when inserting
    if (/^[a-zA-Z0-9._\s-]*$/.test(afterAt)) {
      return {
        start: lastAtIndex,
        end: cursor,
        query: afterAt
      }
    }

    return null
  }, [completedMentions])

  // Convert raw value (with emails) to display value (with names) for UI
  const convertRawToDisplay = useCallback((rawVal: string): string => {
    if (!participantsData?.content?.length) return rawVal

    let result = rawVal
    const participants = participantsData.content

    // Create a map for efficient lookup
    const participantMap = new Map<string, HubParticipantResponse>()
    participants.forEach(p => {
      if (p.email) {
        participantMap.set(p.email.toLowerCase(), p)
        // Also map by email prefix for backward compatibility
        const emailPrefix = p.email.split('@')[0].toLowerCase()
        participantMap.set(emailPrefix, p)
      }
    })

    // Replace email-based mentions with name-based display
    // Pattern matches both @<EMAIL> and @user formats
    const mentionRegex = /@([a-zA-Z0-9._-]+(?:@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})?)/g

    result = result.replace(mentionRegex, (match, identifier) => {
      const participant = participantMap.get(identifier.toLowerCase())
      if (participant && participant.name) {
        return `@${participant.name}`
      }
      return match // Return original if no participant found
    })

    return result
  }, [participantsData?.content])

  // Helper function to escape special regex characters
  const escapeRegExp = (string: string): string => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // Update mention positions when text changes
  const updateMentionPositions = useCallback((oldText: string, newText: string, changeStart: number): MentionData[] => {
    const lengthDiff = newText.length - oldText.length

    return mentions.map(mention => {
      // If the change is before this mention, adjust its position
      if (changeStart <= mention.start) {
        return {
          ...mention,
          start: mention.start + lengthDiff,
          end: mention.end + lengthDiff
        }
      }
      // If the change is within this mention, the mention might be corrupted
      // For now, we'll keep it as-is, but in a more robust implementation
      // we might want to remove corrupted mentions
      return mention
    })
  }, [mentions])

  // Convert display input back to raw value (preserving mention emails)
  const convertDisplayToRaw = useCallback((displayVal: string): string => {
    if (!mentions.length) return displayVal

    let result = displayVal

    // Sort mentions by position (reverse order to maintain positions during replacement)
    const sortedMentions = [...mentions].sort((a, b) => b.start - a.start)

    // Replace display mentions with email mentions
    for (const mention of sortedMentions) {
      const displayText = mention.displayText // e.g., "@Pavel"
      const rawText = mention.rawText // e.g., "@<EMAIL>"

      // Find and replace the display mention with the email mention
      // Use a more precise replacement to avoid partial matches
      const displayIndex = result.indexOf(displayText)
      if (displayIndex !== -1) {
        // Check if this is a complete mention (not part of another word)
        const beforeChar = displayIndex > 0 ? result[displayIndex - 1] : ' '
        const afterIndex = displayIndex + displayText.length
        const afterChar = afterIndex < result.length ? result[afterIndex] : ' '

        // Only replace if it's a complete mention (surrounded by spaces or boundaries)
        if ((beforeChar === ' ' || displayIndex === 0) &&
            (afterChar === ' ' || afterChar === '\n' || afterIndex === result.length)) {
          result = result.substring(0, displayIndex) + rawText + result.substring(afterIndex)
        }
      }
    }

    return result
  }, [mentions])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newDisplayValue = e.target.value
    const cursor = e.target.selectionStart ?? 0

    // Update mention positions based on the change
    const updatedMentions = updateMentionPositions(displayValue, newDisplayValue, cursor)
    setMentions(updatedMentions)

    // Convert display input back to raw value (preserving mention emails)
    const newRawValue = convertDisplayToRaw(newDisplayValue)

    // Update both values
    setDisplayValue(newDisplayValue)
    setRawValue(newRawValue)

    // Notify parent with raw value (email-based, source of truth)
    onChange(newRawValue)

    // Clear completed mentions if the text has changed significantly
    if (Math.abs(newDisplayValue.length - displayValue.length) > 1) {
      setCompletedMentions(new Set())
    }

    // Skip mention processing if we're currently inserting a mention
    if (isInsertingMention) {
      return
    }

    // Check if space was typed after a mention to close dropdown
    if (newDisplayValue.length > displayValue.length && newDisplayValue[cursor - 1] === ' ' && isOpen) {
      // Space was typed, close the popup and mark the position as completed
      const lastAtIndex = newDisplayValue.lastIndexOf('@', cursor - 2)
      if (lastAtIndex !== -1) {
        setCompletedMentions(prev => new Set([...prev, lastAtIndex]))
      }
      setIsOpen(false)
      setMentionMatch(null)
      setSelectedIndex(0)
      return
    }

    const match = findMentionMatch(newDisplayValue, cursor)
    setMentionMatch(match)
    setIsOpen(!!match)
    setSelectedIndex(0)
  }, [onChange, findMentionMatch, displayValue, isOpen, isInsertingMention, convertDisplayToRaw, updateMentionPositions])

  const insertMention = useCallback((participant: HubParticipantResponse) => {
    if (!mentionMatch || !inputRef.current) return

    // Set insertion flag to prevent dropdown from reopening
    setIsInsertingMention(true)

    // Work with raw value (email-based) as source of truth
    const beforeMention = rawValue.slice(0, mentionMatch.start)
    const afterMention = rawValue.slice(mentionMatch.end)

    // Create email-based mention text (source of truth)
    const emailMentionText = `@${participant.email || '<EMAIL>'}`
    const participantName = participant.name || participant.email?.split('@')[0] || 'Unknown'
    const displayMentionText = `@${participantName}`

    // Create new raw value with email address
    const newRawValue = beforeMention + emailMentionText + ' ' + afterMention

    // Create mention data to track this mention
    const newMention: MentionData = {
      participant,
      start: mentionMatch.start,
      end: mentionMatch.start + emailMentionText.length,
      displayText: displayMentionText,
      rawText: emailMentionText
    }

    // Update mentions array to track this mention
    const updatedMentions = [...mentions, newMention]
    setMentions(updatedMentions)

    // Mark this mention position as completed to prevent dropdown from reopening
    setCompletedMentions(prev => new Set([...prev, mentionMatch.start]))

    // Close dropdown immediately
    setIsOpen(false)
    setMentionMatch(null)
    setSelectedIndex(0)

    // Update raw value (source of truth)
    setRawValue(newRawValue)

    // Convert to display value for UI
    const newDisplayValue = convertRawToDisplay(newRawValue)
    setDisplayValue(newDisplayValue)

    // Notify parent with raw value (email-based, source of truth)
    onChange(newRawValue)

    // Position cursor after the mention in display value and reset insertion flag
    const newCursor = mentionMatch.start + displayMentionText.length + 1 // +1 for space

    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.setSelectionRange(newCursor, newCursor)
        inputRef.current.focus()
      }
      // Reset insertion flag after DOM updates
      setIsInsertingMention(false)
    }, 0)
  }, [mentionMatch, rawValue, onChange, convertRawToDisplay, mentions])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (isOpen && filteredParticipants.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % filteredParticipants.length)
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + filteredParticipants.length) % filteredParticipants.length)
          break
        case 'Enter':
        case 'Tab':
          e.preventDefault()
          insertMention(filteredParticipants[selectedIndex])
          break
        case 'Escape':
          e.preventDefault()
          setIsOpen(false)
          setMentionMatch(null)
          setSelectedIndex(0)
          break
        case ' ':
          // Space key - close the popup if we're at the end of a mention
          if (mentionMatch) {
            setIsOpen(false)
            setMentionMatch(null)
            setSelectedIndex(0)
          }
          onKeyDown?.(e)
          break
        default:
          onKeyDown?.(e)
      }
    } else {
      onKeyDown?.(e)
    }
  }, [isOpen, filteredParticipants, selectedIndex, mentionMatch, onKeyDown, insertMention])


  const handleParticipantSelect = useCallback((e: React.MouseEvent, p: HubParticipantResponse) => {
    e.preventDefault()
    e.stopPropagation()
    insertMention(p)
  }, [insertMention])

  const handleSelect = useCallback(() => {
    // Don't interfere with mention selection when popup is open
    if (isOpen) {
      return
    }

    // Only update the mention match, don't close the popup aggressively
    setTimeout(() => {
      const cursor = inputRef.current?.selectionStart ?? 0
      const match = findMentionMatch(displayValue, cursor)

      // Only close if we're clearly outside a mention context
      if (!match && mentionMatch) {
        // Check if cursor moved outside the mention range
        const mentionEnd = mentionMatch.start + mentionMatch.query.length + 1 // +1 for @
        if (cursor < mentionMatch.start || cursor > mentionEnd + 2) { // +2 for some tolerance
          setIsOpen(false)
          setMentionMatch(null)
          setSelectedIndex(0)
        }
      } else if (match) {
        setMentionMatch(match)
        if (!isOpen) {
          setIsOpen(true)
          setSelectedIndex(0)
        }
      }
    }, 0)
  }, [displayValue, findMentionMatch, mentionMatch, isOpen])

  // Sync values when external value changes (raw value is source of truth)
  useEffect(() => {
    if (value !== rawValue) {
      setRawValue(value) // Raw value is source of truth
      const newDisplayValue = convertRawToDisplay(value)
      setDisplayValue(newDisplayValue) // Display value is derived
      // Reset insertion flag, completed mentions, and mentions array when value changes externally
      setIsInsertingMention(false)
      setCompletedMentions(new Set())
      setMentions([]) // Clear mentions array since we're starting fresh
    }
  }, [value, rawValue, convertRawToDisplay])

  // Initialize display value from raw value on mount and when participants load
  useEffect(() => {
    const initialDisplayValue = convertRawToDisplay(value)
    setDisplayValue(initialDisplayValue)
  }, [value, convertRawToDisplay]) // Re-run when participants load

  // Cleanup insertion flag on unmount
  useEffect(() => {
    return () => {
      setIsInsertingMention(false)
      setCompletedMentions(new Set())
      setMentions([])
    }
  }, [])

  return (
    <div className={cn("relative", className)}>
      {children({
        ref: inputRef,
        value: rawValue, // Raw value (email-based, source of truth)
        displayValue: displayValue, // Display value (name-based, for UI)
        onChange: handleInputChange,
        onKeyDown: handleKeyDown,
        onSelect: handleSelect,
        placeholder,
        disabled,
        className
      })}



      {isOpen && (
        <div
          ref={popoverRef}
          className="absolute z-50 w-80 bottom-full mb-1 bg-popover border border-border rounded-md shadow-md"
        >
          <div className="p-2">
            {isLoading ? (
              <div className="text-center py-4 text-sm text-muted-foreground">
                {t(keys.ui.mentionInput.loadingParticipants)}
              </div>
            ) : filteredParticipants.length === 0 ? (
              <div className="text-center py-4 text-sm text-muted-foreground">
                {t(keys.ui.mentionInput.noParticipantsFound)}
              </div>
            ) : (
              <ScrollArea className="max-h-64">
                {filteredParticipants.map((p, index) => (
                  <div
                    key={p.id}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleParticipantSelect(e, p)
                    }}
                    className={cn(
                      "flex items-center gap-3 p-3 cursor-pointer rounded-md hover:bg-accent",
                      index === selectedIndex && "bg-accent"
                    )}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {getInitials(p.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm truncate">
                          {p.name || 'Unknown'}
                        </p>
                        {p.role && <span>{getRoleIcon(p.role)}</span>}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-muted-foreground truncate">
                          {p.email}
                        </p>
                        {p.role && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {getRoleLabel(p.role)}
                          </Badge>
                        )}
                        {p.isExternal && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            External
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </ScrollArea>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
